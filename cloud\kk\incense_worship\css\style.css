/* 网络烧香拜佛系统样式 */

/* 基础重置和通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
}

.btn-full {
    width: 100%;
}

/* 顶部导航 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 25px;
}

.site-title {
    font-size: 28px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.username {
    font-weight: bold;
    color: #2d3436;
}

.gongde {
    background: linear-gradient(45deg, #fdcb6e, #e17055);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    font-size: 16px;
}

/* 提示框 */
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 10px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(45deg, #00b894, #00a085);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.alert-error {
    background: linear-gradient(45deg, #e17055, #d63031);
    color: white;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
}

/* 主要内容区域 */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 佛像区域 */
.buddha-section h2,
.incense-section h2 {
    text-align: center;
    margin-bottom: 25px;
    font-size: 24px;
    background: linear-gradient(45deg, #6c5ce7, #a29bfe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.buddha-grid,
.incense-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.buddha-card,
.incense-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.buddha-card:hover,
.incense-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border-color: #ff6b6b;
}

.buddha-image,
.incense-image {
    text-align: center;
    margin-bottom: 15px;
}

.buddha-image img,
.incense-image img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #ff6b6b;
}

.buddha-info h3,
.incense-info h3 {
    text-align: center;
    margin-bottom: 10px;
    color: #2d3436;
}

.buddha-blessing,
.buddha-bonus,
.incense-cost,
.incense-reward,
.incense-duration {
    font-size: 13px;
    margin: 5px 0;
    text-align: center;
}

.buddha-bonus {
    color: #e17055;
    font-weight: bold;
}

.incense-cost {
    color: #d63031;
}

.incense-reward {
    color: #00b894;
}

.incense-duration {
    color: #0984e3;
}

.buddha-desc,
.incense-desc {
    font-size: 12px;
    color: #636e72;
    text-align: center;
    margin: 10px 0;
}

.buddha-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

/* 香品稀有度样式 */
.rarity-common {
    border-left: 4px solid #74b9ff;
}

.rarity-rare {
    border-left: 4px solid #a29bfe;
}

.rarity-epic {
    border-left: 4px solid #fd79a8;
}

.rarity-legendary {
    border-left: 4px solid #fdcb6e;
    background: linear-gradient(135deg, #fff 0%, #fff9e6 100%);
}

/* 正在燃烧的香 */
.burning-incense-section {
    margin: 30px 0;
    padding: 20px;
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    border-radius: 15px;
}

.burning-incense-section h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #2d3436;
}

.burning-incense-list {
    display: grid;
    gap: 15px;
}

.burning-incense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.burning-timer {
    text-align: center;
}

.timer-display {
    font-size: 18px;
    font-weight: bold;
    color: #e17055;
    margin-bottom: 5px;
}

.progress-bar {
    width: 150px;
    height: 8px;
    background: #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 4px;
    transition: width 1s ease;
}

/* 统计信息 */
.stats-section {
    margin-top: 30px;
}

.stats-section h2 {
    text-align: center;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #00b894, #00a085);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-card h3 {
    margin-bottom: 10px;
    color: #2d3436;
    font-size: 16px;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 10px 0;
}

.stat-label {
    color: #636e72;
    font-size: 14px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #ff6b6b;
}

.modal h2 {
    margin-bottom: 20px;
    text-align: center;
    background: linear-gradient(45deg, #6c5ce7, #a29bfe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2d3436;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.form-group textarea {
    height: 100px;
    resize: vertical;
}

.cost-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    color: #856404;
    margin: 15px 0;
}

/* 登录页面样式 */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 450px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    font-size: 32px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
}

.subtitle {
    color: #636e72;
    font-size: 16px;
}

.auth-tabs {
    display: flex;
    margin-bottom: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
}

.tab-btn {
    flex: 1;
    padding: 12px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.login-footer {
    margin-top: 30px;
    text-align: center;
}

.buddhist-quotes p {
    color: #636e72;
    font-style: italic;
    margin: 5px 0;
}

.quote-source {
    font-size: 12px;
    color: #b2bec3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .nav-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .user-info {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .buddha-grid,
    .incense-grid,
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .burning-incense-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
        padding: 20px;
    }
    
    .login-container {
        margin: 20px;
        padding: 30px 20px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.main-content {
    animation: fadeIn 0.6s ease;
}

.buddha-card,
.incense-card,
.stat-card {
    animation: slideUp 0.6s ease;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ee5a24, #d63031);
}

/* 特殊效果 */
.worship-area {
    position: relative;
}

.worship-area::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24, #fdcb6e, #74b9ff);
    background-size: 400% 400%;
    border-radius: 25px;
    z-index: -1;
    animation: gradientShift 8s ease infinite;
    opacity: 0.1;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
