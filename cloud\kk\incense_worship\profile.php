<?php
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/incense.php';

$auth = new Auth();
$incenseService = new IncenseService();

// 检查用户是否登录
if (!isLoggedIn()) {
    redirectTo('login.php');
}

$userId = getCurrentUserId();
$userInfo = $auth->getUserInfo($userId);

// 获取用户许愿记录
$wishes = $incenseService->getUserWishes($userId, null, 10);

// 获取用户烧香历史
$incenseHistory = $incenseService->getUserIncenseHistory($userId, 10);

// 获取功德记录
$gongdeRecords = $incenseService->getGongdeRecords($userId, 10);

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_profile'])) {
        $result = $auth->updateUserInfo($userId, $_POST['nickname'], $_POST['email']);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'error';
        
        if ($result['success']) {
            $userInfo = $auth->getUserInfo($userId); // 重新获取用户信息
            $_SESSION['nickname'] = $_POST['nickname'];
        }
    } elseif (isset($_POST['change_password'])) {
        $result = $auth->changePassword($userId, $_POST['old_password'], $_POST['new_password']);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'error';
    } elseif (isset($_POST['fulfill_wish'])) {
        $result = $incenseService->fulfillWish($userId, $_POST['wish_id']);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'error';
        
        if ($result['success']) {
            $wishes = $incenseService->getUserWishes($userId, null, 10); // 重新获取许愿记录
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="nav-container">
                <h1 class="site-title"><?php echo SITE_NAME; ?></h1>
                <div class="user-info">
                    <span class="username"><?php echo htmlspecialchars($userInfo['nickname']); ?></span>
                    <span class="gongde">功德: <?php echo $userInfo['gongde_points']; ?></span>
                    <a href="index.php" class="btn btn-primary">返回主页</a>
                    <a href="logout.php" class="btn btn-secondary">退出</a>
                </div>
            </div>
        </header>

        <!-- 消息提示 -->
        <?php if (isset($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- 主要内容 -->
        <main class="main-content">
            <div class="profile-tabs">
                <button class="tab-btn active" data-tab="info">个人信息</button>
                <button class="tab-btn" data-tab="wishes">我的许愿</button>
                <button class="tab-btn" data-tab="history">烧香历史</button>
                <button class="tab-btn" data-tab="gongde">功德记录</button>
                <button class="tab-btn" data-tab="settings">账户设置</button>
            </div>

            <!-- 个人信息标签页 -->
            <div id="info-tab" class="tab-content active">
                <div class="profile-info">
                    <h2>个人信息</h2>
                    <div class="info-grid">
                        <div class="info-card">
                            <h3>基本信息</h3>
                            <div class="info-item">
                                <label>用户名:</label>
                                <span><?php echo htmlspecialchars($userInfo['username']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>昵称:</label>
                                <span><?php echo htmlspecialchars($userInfo['nickname']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>邮箱:</label>
                                <span><?php echo htmlspecialchars($userInfo['email']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>注册时间:</label>
                                <span><?php echo date('Y-m-d H:i', strtotime($userInfo['created_at'])); ?></span>
                            </div>
                        </div>
                        
                        <div class="info-card">
                            <h3>功德统计</h3>
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $userInfo['gongde_points']; ?></div>
                                    <div class="stat-label">当前功德</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $userInfo['total_incense']; ?></div>
                                    <div class="stat-label">累计烧香</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $userInfo['total_worship']; ?></div>
                                    <div class="stat-label">累计拜佛</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $userInfo['total_wishes']; ?></div>
                                    <div class="stat-label">累计许愿</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的许愿标签页 -->
            <div id="wishes-tab" class="tab-content">
                <h2>我的许愿</h2>
                <div class="wishes-list">
                    <?php if (empty($wishes)): ?>
                    <div class="empty-state">
                        <p>您还没有许过愿，<a href="index.php">去许愿</a></p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($wishes as $wish): ?>
                    <div class="wish-card <?php echo $wish['status']; ?>">
                        <div class="wish-header">
                            <h4><?php echo htmlspecialchars($wish['wish_category']); ?></h4>
                            <span class="wish-status status-<?php echo $wish['status']; ?>">
                                <?php
                                $statusText = [
                                    'pending' => '等待实现',
                                    'fulfilled' => '已实现',
                                    'expired' => '已过期'
                                ];
                                echo $statusText[$wish['status']];
                                ?>
                            </span>
                        </div>
                        <div class="wish-content">
                            <p><?php echo htmlspecialchars($wish['wish_content']); ?></p>
                        </div>
                        <div class="wish-meta">
                            <span>许愿给: <?php echo htmlspecialchars($wish['buddha_name']); ?></span>
                            <span>时间: <?php echo date('Y-m-d', strtotime($wish['created_at'])); ?></span>
                            <?php if ($wish['status'] == 'pending'): ?>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="wish_id" value="<?php echo $wish['id']; ?>">
                                <button type="submit" name="fulfill_wish" class="btn btn-small btn-primary">标记为已实现</button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 烧香历史标签页 -->
            <div id="history-tab" class="tab-content">
                <h2>烧香历史</h2>
                <div class="history-list">
                    <?php if (empty($incenseHistory)): ?>
                    <div class="empty-state">
                        <p>您还没有烧香记录，<a href="index.php">去烧香</a></p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($incenseHistory as $record): ?>
                    <div class="history-card status-<?php echo $record['status']; ?>">
                        <div class="history-header">
                            <h4><?php echo htmlspecialchars($record['incense_name']); ?></h4>
                            <span class="history-status">
                                <?php
                                $statusText = [
                                    'burning' => '燃烧中',
                                    'completed' => '已完成',
                                    'expired' => '已过期'
                                ];
                                echo $statusText[$record['status']];
                                ?>
                            </span>
                        </div>
                        <div class="history-content">
                            <p><strong>敬献给:</strong> <?php echo htmlspecialchars($record['buddha_name']); ?></p>
                            <?php if ($record['wish_content']): ?>
                            <p><strong>许愿:</strong> <?php echo htmlspecialchars($record['wish_content']); ?></p>
                            <?php endif; ?>
                            <p><strong>消耗功德:</strong> <?php echo $record['gongde_cost']; ?> 点</p>
                            <p><strong>奖励功德:</strong> <?php echo $record['gongde_reward']; ?> 点</p>
                        </div>
                        <div class="history-meta">
                            <span>开始时间: <?php echo date('Y-m-d H:i', strtotime($record['start_time'])); ?></span>
                            <?php if ($record['end_time']): ?>
                            <span>结束时间: <?php echo date('Y-m-d H:i', strtotime($record['end_time'])); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 功德记录标签页 -->
            <div id="gongde-tab" class="tab-content">
                <h2>功德记录</h2>
                <div class="gongde-list">
                    <?php if (empty($gongdeRecords)): ?>
                    <div class="empty-state">
                        <p>暂无功德记录</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($gongdeRecords as $record): ?>
                    <div class="gongde-card">
                        <div class="gongde-change <?php echo $record['points_change'] > 0 ? 'positive' : 'negative'; ?>">
                            <?php echo $record['points_change'] > 0 ? '+' : ''; ?><?php echo $record['points_change']; ?>
                        </div>
                        <div class="gongde-info">
                            <div class="gongde-desc"><?php echo htmlspecialchars($record['description']); ?></div>
                            <div class="gongde-time"><?php echo timeAgo($record['created_at']); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 账户设置标签页 -->
            <div id="settings-tab" class="tab-content">
                <h2>账户设置</h2>
                <div class="settings-grid">
                    <!-- 个人信息修改 -->
                    <div class="settings-card">
                        <h3>修改个人信息</h3>
                        <form method="POST" class="settings-form">
                            <div class="form-group">
                                <label>昵称:</label>
                                <input type="text" name="nickname" value="<?php echo htmlspecialchars($userInfo['nickname']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label>邮箱:</label>
                                <input type="email" name="email" value="<?php echo htmlspecialchars($userInfo['email']); ?>" required>
                            </div>
                            <button type="submit" name="update_profile" class="btn btn-primary">更新信息</button>
                        </form>
                    </div>

                    <!-- 密码修改 -->
                    <div class="settings-card">
                        <h3>修改密码</h3>
                        <form method="POST" class="settings-form">
                            <div class="form-group">
                                <label>原密码:</label>
                                <input type="password" name="old_password" required>
                            </div>
                            <div class="form-group">
                                <label>新密码:</label>
                                <input type="password" name="new_password" required minlength="6">
                            </div>
                            <div class="form-group">
                                <label>确认新密码:</label>
                                <input type="password" name="confirm_password" required minlength="6">
                            </div>
                            <button type="submit" name="change_password" class="btn btn-primary">修改密码</button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 标签页切换功能
        $(document).ready(function() {
            $('.tab-btn').click(function() {
                const tab = $(this).data('tab');
                
                // 移除所有活动状态
                $('.tab-btn').removeClass('active');
                $('.tab-content').removeClass('active');
                
                // 添加活动状态
                $(this).addClass('active');
                $('#' + tab + '-tab').addClass('active');
            });

            // 密码确认验证
            $('form').submit(function(e) {
                const newPassword = $('input[name="new_password"]').val();
                const confirmPassword = $('input[name="confirm_password"]').val();
                
                if (newPassword && confirmPassword && newPassword !== confirmPassword) {
                    e.preventDefault();
                    alert('两次输入的密码不一致');
                    return false;
                }
            });
        });
    </script>

    <style>
        /* 个人中心特定样式 */
        .profile-tabs {
            display: flex;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            overflow-x: auto;
        }

        .profile-tabs .tab-btn {
            flex: 1;
            min-width: 120px;
            padding: 12px 20px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .profile-tabs .tab-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .info-item label {
            font-weight: bold;
            color: #2d3436;
        }

        .stat-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b6b;
        }

        .stat-label {
            font-size: 12px;
            color: #636e72;
            margin-top: 5px;
        }

        .wish-card, .history-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .wish-header, .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .wish-status, .history-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-pending {
            background: #ffeaa7;
            color: #856404;
        }

        .status-fulfilled {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }

        .wish-meta, .history-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            font-size: 12px;
            color: #636e72;
        }

        .gongde-card {
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .gongde-change {
            font-size: 18px;
            font-weight: bold;
            margin-right: 15px;
            min-width: 60px;
        }

        .gongde-change.positive {
            color: #00b894;
        }

        .gongde-change.negative {
            color: #d63031;
        }

        .gongde-info {
            flex: 1;
        }

        .gongde-desc {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .gongde-time {
            font-size: 12px;
            color: #636e72;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .settings-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
        }

        .settings-form .form-group {
            margin-bottom: 20px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #636e72;
        }

        .empty-state a {
            color: #ff6b6b;
            text-decoration: none;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .info-grid, .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .wish-meta, .history-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .profile-tabs {
                overflow-x: auto;
            }
        }
    </style>
</body>
</html>
