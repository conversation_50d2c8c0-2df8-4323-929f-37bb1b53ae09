<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

$auth = new Auth();
$error = '';
$success = '';

// 如果已经登录，重定向到主页
if (isLoggedIn()) {
    redirectTo('index.php');
}

// 处理登录表单提交
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['login'])) {
        $username = sanitizeInput($_POST['username']);
        $password = $_POST['password'];
        
        if (empty($username) || empty($password)) {
            $error = '请填写完整的登录信息';
        } else {
            $result = $auth->login($username, $password);
            if ($result['success']) {
                redirectTo('index.php');
            } else {
                $error = $result['message'];
            }
        }
    } elseif (isset($_POST['register'])) {
        $username = sanitizeInput($_POST['reg_username']);
        $email = sanitizeInput($_POST['reg_email']);
        $password = $_POST['reg_password'];
        $confirmPassword = $_POST['reg_confirm_password'];
        $nickname = sanitizeInput($_POST['reg_nickname']);
        
        if (empty($username) || empty($email) || empty($password)) {
            $error = '请填写完整的注册信息';
        } elseif ($password !== $confirmPassword) {
            $error = '两次输入的密码不一致';
        } elseif (strlen($password) < 6) {
            $error = '密码长度至少6位';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = '邮箱格式不正确';
        } else {
            $result = $auth->register($username, $email, $password, $nickname);
            if ($result['success']) {
                $success = $result['message'] . ' 请登录';
            } else {
                $error = $result['message'];
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-header">
            <h1><?php echo SITE_NAME; ?></h1>
            <p class="subtitle">虔诚礼佛，积累功德</p>
        </div>

        <?php if ($error): ?>
        <div class="alert alert-error">
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <div class="auth-tabs">
            <button class="tab-btn active" data-tab="login">登录</button>
            <button class="tab-btn" data-tab="register">注册</button>
        </div>

        <!-- 登录表单 -->
        <div id="login-tab" class="tab-content active">
            <form method="POST" class="auth-form">
                <div class="form-group">
                    <label>用户名/邮箱:</label>
                    <input type="text" name="username" required 
                           value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                           placeholder="请输入用户名或邮箱">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" name="password" required placeholder="请输入密码">
                </div>
                <button type="submit" name="login" class="btn btn-primary btn-full">登录</button>
            </form>
        </div>

        <!-- 注册表单 -->
        <div id="register-tab" class="tab-content">
            <form method="POST" class="auth-form">
                <div class="form-group">
                    <label>用户名:</label>
                    <input type="text" name="reg_username" required 
                           value="<?php echo isset($_POST['reg_username']) ? htmlspecialchars($_POST['reg_username']) : ''; ?>"
                           placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label>邮箱:</label>
                    <input type="email" name="reg_email" required 
                           value="<?php echo isset($_POST['reg_email']) ? htmlspecialchars($_POST['reg_email']) : ''; ?>"
                           placeholder="请输入邮箱地址">
                </div>
                <div class="form-group">
                    <label>昵称:</label>
                    <input type="text" name="reg_nickname" 
                           value="<?php echo isset($_POST['reg_nickname']) ? htmlspecialchars($_POST['reg_nickname']) : ''; ?>"
                           placeholder="请输入昵称（可选）">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" name="reg_password" required placeholder="请输入密码（至少6位）">
                </div>
                <div class="form-group">
                    <label>确认密码:</label>
                    <input type="password" name="reg_confirm_password" required placeholder="请再次输入密码">
                </div>
                <button type="submit" name="register" class="btn btn-primary btn-full">注册</button>
            </form>
        </div>

        <div class="login-footer">
            <div class="buddhist-quotes">
                <p>"诸恶莫作，众善奉行，自净其意，是诸佛教"</p>
                <p class="quote-source">- 《大智度论》</p>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tab = this.dataset.tab;
                
                // 移除所有活动状态
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加活动状态
                this.classList.add('active');
                document.getElementById(tab + '-tab').classList.add('active');
            });
        });

        // 如果有注册错误，自动切换到注册标签
        <?php if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['register'])): ?>
        document.querySelector('[data-tab="register"]').click();
        <?php endif; ?>
    </script>
</body>
</html>
