<?php
require_once 'config/database.php';

class IncenseService {
    private $db;
    
    public function __construct() {
        $this->db = getDBConnection();
    }
    
    // 获取所有香品类型
    public function getIncenseTypes() {
        $stmt = $this->db->prepare("
            SELECT id, name, description, image, gongde_cost, gongde_reward, 
                   duration_minutes, rarity
            FROM incense_types 
            WHERE status = 'active' 
            ORDER BY rarity DESC, gongde_cost ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    // 获取所有佛像
    public function getBuddhas() {
        $stmt = $this->db->prepare("
            SELECT id, name, description, image, blessing_type, gongde_bonus
            FROM buddhas 
            WHERE status = 'active' 
            ORDER BY gongde_bonus DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    // 烧香
    public function burnIncense($userId, $incenseTypeId, $buddhaId, $wishContent = '') {
        try {
            $this->db->beginTransaction();
            
            // 检查用户功德是否足够
            $stmt = $this->db->prepare("SELECT gongde_points FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            // 获取香品信息
            $stmt = $this->db->prepare("
                SELECT gongde_cost, gongde_reward, duration_minutes, name 
                FROM incense_types WHERE id = ?
            ");
            $stmt->execute([$incenseTypeId]);
            $incense = $stmt->fetch();
            
            if (!$incense) {
                throw new Exception('香品不存在');
            }
            
            if ($user['gongde_points'] < $incense['gongde_cost']) {
                throw new Exception('功德不足，无法烧香');
            }
            
            // 检查今日烧香次数限制
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count FROM incense_records 
                WHERE user_id = ? AND DATE(created_at) = CURDATE()
            ");
            $stmt->execute([$userId]);
            $dailyCount = $stmt->fetch()['count'];
            
            if ($dailyCount >= 10) { // 每日最多10次
                throw new Exception('今日烧香次数已达上限');
            }
            
            // 计算结束时间
            $endTime = date('Y-m-d H:i:s', time() + ($incense['duration_minutes'] * 60));
            
            // 插入烧香记录
            $stmt = $this->db->prepare("
                INSERT INTO incense_records 
                (user_id, incense_type_id, buddha_id, gongde_cost, gongde_reward, 
                 wish_content, end_time, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 'burning')
            ");
            $stmt->execute([
                $userId, $incenseTypeId, $buddhaId, 
                $incense['gongde_cost'], $incense['gongde_reward'],
                $wishContent, $endTime
            ]);
            
            $recordId = $this->db->lastInsertId();
            
            // 扣除功德
            $stmt = $this->db->prepare("
                UPDATE users SET 
                    gongde_points = gongde_points - ?, 
                    total_incense = total_incense + 1 
                WHERE id = ?
            ");
            $stmt->execute([$incense['gongde_cost'], $userId]);
            
            // 记录功德消耗
            $this->recordGongde($userId, 'incense', -$incense['gongde_cost'], 
                              "烧{$incense['name']}消耗功德", $recordId);
            
            $this->db->commit();
            
            return [
                'success' => true, 
                'message' => '烧香成功！香将燃烧' . $incense['duration_minutes'] . '分钟',
                'record_id' => $recordId,
                'end_time' => $endTime,
                'gongde_cost' => $incense['gongde_cost']
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    // 检查香是否燃尽并给予奖励
    public function checkIncenseStatus($userId) {
        try {
            $this->db->beginTransaction();
            
            // 查找已燃尽但未完成的香
            $stmt = $this->db->prepare("
                SELECT id, gongde_reward, incense_type_id
                FROM incense_records 
                WHERE user_id = ? AND status = 'burning' AND end_time <= NOW()
            ");
            $stmt->execute([$userId]);
            $completedIncense = $stmt->fetchAll();
            
            $totalReward = 0;
            $completedCount = 0;
            
            foreach ($completedIncense as $incense) {
                // 更新状态为已完成
                $stmt = $this->db->prepare("
                    UPDATE incense_records SET status = 'completed' WHERE id = ?
                ");
                $stmt->execute([$incense['id']]);
                
                // 给予功德奖励
                $stmt = $this->db->prepare("
                    UPDATE users SET gongde_points = gongde_points + ? WHERE id = ?
                ");
                $stmt->execute([$incense['gongde_reward'], $userId]);
                
                // 记录功德获得
                $this->recordGongde($userId, 'incense', $incense['gongde_reward'], 
                                  '烧香完成获得功德奖励', $incense['id']);
                
                $totalReward += $incense['gongde_reward'];
                $completedCount++;
            }
            
            $this->db->commit();
            
            if ($completedCount > 0) {
                return [
                    'success' => true,
                    'completed_count' => $completedCount,
                    'total_reward' => $totalReward,
                    'message' => "有{$completedCount}支香已燃尽，获得{$totalReward}功德奖励"
                ];
            }
            
            return ['success' => true, 'completed_count' => 0];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    // 获取用户当前燃烧的香
    public function getUserBurningIncense($userId) {
        $stmt = $this->db->prepare("
            SELECT ir.id, ir.wish_content, ir.start_time, ir.end_time,
                   it.name as incense_name, it.image as incense_image,
                   b.name as buddha_name, b.image as buddha_image,
                   TIMESTAMPDIFF(SECOND, NOW(), ir.end_time) as remaining_seconds
            FROM incense_records ir
            JOIN incense_types it ON ir.incense_type_id = it.id
            JOIN buddhas b ON ir.buddha_id = b.id
            WHERE ir.user_id = ? AND ir.status = 'burning'
            ORDER BY ir.end_time ASC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }
    
    // 获取用户烧香历史
    public function getUserIncenseHistory($userId, $limit = 20, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT ir.id, ir.wish_content, ir.start_time, ir.end_time, ir.status,
                   ir.gongde_cost, ir.gongde_reward,
                   it.name as incense_name, it.image as incense_image,
                   b.name as buddha_name, b.blessing_type
            FROM incense_records ir
            JOIN incense_types it ON ir.incense_type_id = it.id
            JOIN buddhas b ON ir.buddha_id = b.id
            WHERE ir.user_id = ?
            ORDER BY ir.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    // 拜佛
    public function worship($userId, $buddhaId, $worshipType = 'bow', $prayerContent = '') {
        try {
            // 检查今日拜佛次数
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count FROM worship_records 
                WHERE user_id = ? AND DATE(created_at) = CURDATE()
            ");
            $stmt->execute([$userId]);
            $dailyCount = $stmt->fetch()['count'];
            
            if ($dailyCount >= 20) { // 每日最多20次
                throw new Exception('今日拜佛次数已达上限');
            }
            
            // 获取佛像信息
            $stmt = $this->db->prepare("SELECT name, gongde_bonus FROM buddhas WHERE id = ?");
            $stmt->execute([$buddhaId]);
            $buddha = $stmt->fetch();
            
            if (!$buddha) {
                throw new Exception('佛像不存在');
            }
            
            // 计算功德奖励
            $baseReward = 5;
            $worshipMultiplier = ['bow' => 1, 'kneel' => 1.5, 'prostrate' => 2];
            $totalReward = floor($baseReward * $worshipMultiplier[$worshipType] + $buddha['gongde_bonus']);
            
            // 插入拜佛记录
            $stmt = $this->db->prepare("
                INSERT INTO worship_records 
                (user_id, buddha_id, worship_type, gongde_reward, prayer_content) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $buddhaId, $worshipType, $totalReward, $prayerContent]);
            
            $recordId = $this->db->lastInsertId();
            
            // 更新用户功德和拜佛次数
            $stmt = $this->db->prepare("
                UPDATE users SET 
                    gongde_points = gongde_points + ?, 
                    total_worship = total_worship + 1 
                WHERE id = ?
            ");
            $stmt->execute([$totalReward, $userId]);
            
            // 记录功德获得
            $this->recordGongde($userId, 'worship', $totalReward, 
                              "拜{$buddha['name']}获得功德", $recordId);
            
            return [
                'success' => true,
                'message' => "拜佛成功！获得{$totalReward}功德",
                'gongde_reward' => $totalReward
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    // 许愿
    public function makeWish($userId, $buddhaId, $wishCategory, $wishContent) {
        try {
            // 检查用户功德是否足够
            $stmt = $this->db->prepare("SELECT gongde_points FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            $wishCost = 20; // 许愿消耗20功德
            
            if ($user['gongde_points'] < $wishCost) {
                throw new Exception('功德不足，无法许愿');
            }
            
            // 检查今日许愿次数
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count FROM wishes 
                WHERE user_id = ? AND DATE(created_at) = CURDATE()
            ");
            $stmt->execute([$userId]);
            $dailyCount = $stmt->fetch()['count'];
            
            if ($dailyCount >= 3) { // 每日最多3次
                throw new Exception('今日许愿次数已达上限');
            }
            
            // 计算过期日期
            $expireDate = date('Y-m-d', strtotime('+1 year'));
            
            $this->db->beginTransaction();
            
            // 插入许愿记录
            $stmt = $this->db->prepare("
                INSERT INTO wishes 
                (user_id, buddha_id, wish_category, wish_content, gongde_cost, expire_date) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $buddhaId, $wishCategory, $wishContent, $wishCost, $expireDate]);
            
            $wishId = $this->db->lastInsertId();
            
            // 扣除功德
            $stmt = $this->db->prepare("
                UPDATE users SET 
                    gongde_points = gongde_points - ?, 
                    total_wishes = total_wishes + 1 
                WHERE id = ?
            ");
            $stmt->execute([$wishCost, $userId]);
            
            // 记录功德消耗
            $this->recordGongde($userId, 'wish', -$wishCost, '许愿消耗功德', $wishId);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => "许愿成功！消耗{$wishCost}功德",
                'wish_id' => $wishId
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    // 获取用户许愿记录
    public function getUserWishes($userId, $status = null, $limit = 20, $offset = 0) {
        $whereClause = "WHERE w.user_id = ?";
        $params = [$userId];
        
        if ($status) {
            $whereClause .= " AND w.status = ?";
            $params[] = $status;
        }
        
        $stmt = $this->db->prepare("
            SELECT w.id, w.wish_category, w.wish_content, w.status, w.fulfill_date, 
                   w.expire_date, w.created_at, w.gongde_cost,
                   b.name as buddha_name, b.blessing_type
            FROM wishes w
            JOIN buddhas b ON w.buddha_id = b.id
            {$whereClause}
            ORDER BY w.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $params = array_merge($params, [$limit, $offset]);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    // 标记愿望实现
    public function fulfillWish($userId, $wishId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE wishes SET status = 'fulfilled', fulfill_date = CURDATE() 
                WHERE id = ? AND user_id = ? AND status = 'pending'
            ");
            $stmt->execute([$wishId, $userId]);
            
            if ($stmt->rowCount() > 0) {
                return ['success' => true, 'message' => '愿望已标记为实现'];
            } else {
                return ['success' => false, 'message' => '愿望不存在或已处理'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    // 记录功德变化
    private function recordGongde($userId, $actionType, $pointsChange, $description, $relatedId = null) {
        $stmt = $this->db->prepare("
            INSERT INTO gongde_records (user_id, action_type, points_change, description, related_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $actionType, $pointsChange, $description, $relatedId]);
    }
    
    // 获取功德记录
    public function getGongdeRecords($userId, $limit = 20, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT action_type, points_change, description, created_at
            FROM gongde_records 
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
}
?>
