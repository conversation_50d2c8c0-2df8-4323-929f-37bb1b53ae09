// 网络烧香拜佛系统前端脚本

$(document).ready(function() {
    // 初始化
    init();
    
    // 检查香的状态
    checkIncenseStatus();
    
    // 每30秒检查一次香的状态
    setInterval(checkIncenseStatus, 30000);
    
    // 更新燃烧计时器
    updateBurningTimers();
    setInterval(updateBurningTimers, 1000);
});

// 初始化函数
function init() {
    // 拜佛按钮事件
    $('.worship-btn').click(function() {
        const buddhaId = $(this).data('buddha-id');
        openWorshipModal(buddhaId);
    });
    
    // 许愿按钮事件
    $('.wish-btn').click(function() {
        const buddhaId = $(this).data('buddha-id');
        openWishModal(buddhaId);
    });
    
    // 烧香按钮事件
    $('.burn-incense-btn').click(function() {
        const incenseId = $(this).data('incense-id');
        openIncenseModal(incenseId);
    });
    
    // 模态框关闭事件
    $('.close').click(function() {
        $(this).closest('.modal').hide();
    });
    
    // 点击模态框外部关闭
    $('.modal').click(function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
    
    // 表单提交事件
    $('#worship-form').submit(handleWorshipSubmit);
    $('#wish-form').submit(handleWishSubmit);
    $('#incense-form').submit(handleIncenseSubmit);
}

// 打开拜佛模态框
function openWorshipModal(buddhaId) {
    $('#worship-buddha-id').val(buddhaId);
    $('#worship-modal').show();
}

// 打开许愿模态框
function openWishModal(buddhaId) {
    $('#wish-buddha-id').val(buddhaId);
    $('#wish-modal').show();
}

// 打开烧香模态框
function openIncenseModal(incenseId) {
    $('#incense-type-id').val(incenseId);
    $('#incense-modal').show();
}

// 处理拜佛表单提交
function handleWorshipSubmit(e) {
    e.preventDefault();
    
    const formData = $(this).serialize() + '&action=worship';
    
    $.post('index.php', formData)
        .done(function(response) {
            if (response.success) {
                showAlert('success', response.message);
                updateGongdePoints(response.gongde_reward);
                $('#worship-modal').hide();
                $('#worship-form')[0].reset();
                // 刷新页面以更新统计
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function() {
            showAlert('error', '网络错误，请重试');
        });
}

// 处理许愿表单提交
function handleWishSubmit(e) {
    e.preventDefault();
    
    const formData = $(this).serialize() + '&action=make_wish';
    
    $.post('index.php', formData)
        .done(function(response) {
            if (response.success) {
                showAlert('success', response.message);
                updateGongdePoints(-20); // 许愿消耗20功德
                $('#wish-modal').hide();
                $('#wish-form')[0].reset();
                // 刷新页面以更新统计
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function() {
            showAlert('error', '网络错误，请重试');
        });
}

// 处理烧香表单提交
function handleIncenseSubmit(e) {
    e.preventDefault();
    
    const formData = $(this).serialize() + '&action=burn_incense';
    
    $.post('index.php', formData)
        .done(function(response) {
            if (response.success) {
                showAlert('success', response.message);
                updateGongdePoints(-response.gongde_cost);
                $('#incense-modal').hide();
                $('#incense-form')[0].reset();
                // 刷新页面以显示新的燃烧香
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function() {
            showAlert('error', '网络错误，请重试');
        });
}

// 检查香的状态
function checkIncenseStatus() {
    $.post('index.php', {action: 'check_status'})
        .done(function(response) {
            if (response.success && response.completed_count > 0) {
                showAlert('success', response.message);
                updateGongdePoints(response.total_reward);
                // 刷新页面以移除已完成的香
                setTimeout(() => location.reload(), 2000);
            }
        })
        .fail(function() {
            console.log('检查香状态失败');
        });
}

// 更新燃烧计时器
function updateBurningTimers() {
    $('.burning-timer').each(function() {
        const endTime = $(this).data('end-time');
        const now = Math.floor(Date.now() / 1000);
        const remaining = endTime - now;
        
        if (remaining <= 0) {
            $(this).find('.timer-display').text('已燃尽');
            $(this).find('.progress-fill').css('width', '100%');
            return;
        }
        
        // 计算剩余时间
        const hours = Math.floor(remaining / 3600);
        const minutes = Math.floor((remaining % 3600) / 60);
        const seconds = remaining % 60;
        
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        $(this).find('.timer-display').text(timeString);
        
        // 更新进度条
        const startTime = endTime - ($(this).closest('.burning-incense-item').data('duration') || 3600);
        const totalDuration = endTime - startTime;
        const elapsed = now - startTime;
        const progress = Math.min(100, (elapsed / totalDuration) * 100);
        
        $(this).find('.progress-fill').css('width', progress + '%');
    });
}

// 更新功德点数显示
function updateGongdePoints(change) {
    const current = parseInt($('#gongde-points').text());
    const newValue = current + change;
    $('#gongde-points').text(newValue);
    
    // 添加动画效果
    if (change > 0) {
        $('#gongde-points').addClass('gongde-increase');
        setTimeout(() => $('#gongde-points').removeClass('gongde-increase'), 1000);
    } else if (change < 0) {
        $('#gongde-points').addClass('gongde-decrease');
        setTimeout(() => $('#gongde-points').removeClass('gongde-decrease'), 1000);
    }
}

// 显示提示信息
function showAlert(type, message) {
    // 移除现有的提示
    $('.alert').remove();
    
    // 创建新的提示
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    const alertHtml = `<div class="alert ${alertClass}">${message}</div>`;
    
    // 插入到页面顶部
    $('.container').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut(500, function() {
            $(this).remove();
        });
    }, 3000);
}

// 功德点数动画样式
const style = document.createElement('style');
style.textContent = `
    .gongde-increase {
        animation: gongdeIncrease 1s ease;
    }
    
    .gongde-decrease {
        animation: gongdeDecrease 1s ease;
    }
    
    @keyframes gongdeIncrease {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); color: #00b894; }
        100% { transform: scale(1); }
    }
    
    @keyframes gongdeDecrease {
        0% { transform: scale(1); }
        50% { transform: scale(0.8); color: #d63031; }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        // 页面变为可见时，检查香的状态
        checkIncenseStatus();
    }
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript错误:', e.error);
});

// AJAX错误处理
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    console.error('AJAX错误:', thrownError);
    if (xhr.status === 403) {
        showAlert('error', '会话已过期，请重新登录');
        setTimeout(() => {
            window.location.href = 'login.php';
        }, 2000);
    }
});

// 键盘事件处理
$(document).keydown(function(e) {
    // ESC键关闭模态框
    if (e.keyCode === 27) {
        $('.modal').hide();
    }
});

// 工具函数：格式化时间
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// 工具函数：获取当前时间戳
function getCurrentTimestamp() {
    return Math.floor(Date.now() / 1000);
}

// 平滑滚动到指定元素
function scrollToElement(selector) {
    $('html, body').animate({
        scrollTop: $(selector).offset().top - 100
    }, 500);
}

// 验证表单输入
function validateForm(formSelector) {
    let isValid = true;
    
    $(formSelector + ' [required]').each(function() {
        if (!$(this).val().trim()) {
            $(this).addClass('error');
            isValid = false;
        } else {
            $(this).removeClass('error');
        }
    });
    
    return isValid;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 本地存储工具
const storage = {
    set: function(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (e) {
            console.warn('无法保存到本地存储:', e);
        }
    },
    
    get: function(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (e) {
            console.warn('无法从本地存储读取:', e);
            return null;
        }
    },
    
    remove: function(key) {
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.warn('无法从本地存储删除:', e);
        }
    }
};

// 页面加载完成后的额外初始化
$(window).on('load', function() {
    // 预加载图片
    preloadImages();
    
    // 添加页面加载动画
    $('body').addClass('loaded');
});

// 预加载图片
function preloadImages() {
    const images = [
        'images/default-buddha.jpg',
        'images/default-incense.jpg'
    ];
    
    images.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// 添加页面加载样式
const loadingStyle = document.createElement('style');
loadingStyle.textContent = `
    body:not(.loaded) {
        opacity: 0;
    }
    
    body.loaded {
        opacity: 1;
        transition: opacity 0.5s ease;
    }
    
    .error {
        border-color: #d63031 !important;
        background-color: #fff5f5 !important;
    }
`;
document.head.appendChild(loadingStyle);
