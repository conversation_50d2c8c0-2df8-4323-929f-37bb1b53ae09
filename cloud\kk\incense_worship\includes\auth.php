<?php
require_once 'config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDBConnection();
    }
    
    // 用户注册
    public function register($username, $email, $password, $nickname = '') {
        try {
            // 检查用户名是否已存在
            $stmt = $this->db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            if ($stmt->rowCount() > 0) {
                return ['success' => false, 'message' => '用户名或邮箱已存在'];
            }
            
            // 加密密码
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // 插入新用户
            $stmt = $this->db->prepare("
                INSERT INTO users (username, email, password, nickname, gongde_points) 
                VALUES (?, ?, ?, ?, 100)
            ");
            $stmt->execute([$username, $email, $hashedPassword, $nickname ?: $username]);
            
            $userId = $this->db->lastInsertId();
            
            // 记录注册奖励功德
            $this->recordGongde($userId, 'daily_bonus', 100, '新用户注册奖励');
            
            return ['success' => true, 'message' => '注册成功！获得100功德点奖励'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => '注册失败：' . $e->getMessage()];
        }
    }
    
    // 用户登录
    public function login($username, $password) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, email, password, nickname, gongde_points, status 
                FROM users 
                WHERE (username = ? OR email = ?) AND status = 'active'
            ");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // 设置会话
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['nickname'] = $user['nickname'];
                $_SESSION['gongde_points'] = $user['gongde_points'];
                
                // 检查是否给予每日登录奖励
                $this->checkDailyBonus($user['id']);
                
                return ['success' => true, 'message' => '登录成功'];
            } else {
                return ['success' => false, 'message' => '用户名/邮箱或密码错误'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => '登录失败：' . $e->getMessage()];
        }
    }
    
    // 用户登出
    public function logout() {
        session_destroy();
        return ['success' => true, 'message' => '已成功登出'];
    }
    
    // 检查每日登录奖励
    private function checkDailyBonus($userId) {
        try {
            // 检查今天是否已经获得过奖励
            $stmt = $this->db->prepare("
                SELECT id FROM gongde_records 
                WHERE user_id = ? AND action_type = 'daily_bonus' 
                AND DATE(created_at) = CURDATE()
            ");
            $stmt->execute([$userId]);
            
            if ($stmt->rowCount() == 0) {
                // 给予每日奖励
                $this->recordGongde($userId, 'daily_bonus', 10, '每日登录奖励');
                
                // 更新用户功德点数
                $stmt = $this->db->prepare("UPDATE users SET gongde_points = gongde_points + 10 WHERE id = ?");
                $stmt->execute([$userId]);
                
                $_SESSION['gongde_points'] += 10;
            }
        } catch (Exception $e) {
            // 静默处理错误，不影响登录
        }
    }
    
    // 记录功德变化
    private function recordGongde($userId, $actionType, $pointsChange, $description, $relatedId = null) {
        $stmt = $this->db->prepare("
            INSERT INTO gongde_records (user_id, action_type, points_change, description, related_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $actionType, $pointsChange, $description, $relatedId]);
    }
    
    // 获取用户信息
    public function getUserInfo($userId) {
        $stmt = $this->db->prepare("
            SELECT id, username, email, nickname, avatar, gongde_points, 
                   total_incense, total_worship, total_wishes, created_at
            FROM users WHERE id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }
    
    // 更新用户信息
    public function updateUserInfo($userId, $nickname, $email) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users SET nickname = ?, email = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$nickname, $email, $userId]);
            return ['success' => true, 'message' => '信息更新成功'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => '更新失败：' . $e->getMessage()];
        }
    }
    
    // 修改密码
    public function changePassword($userId, $oldPassword, $newPassword) {
        try {
            // 验证旧密码
            $stmt = $this->db->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!password_verify($oldPassword, $user['password'])) {
                return ['success' => false, 'message' => '原密码错误'];
            }
            
            // 更新新密码
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$hashedPassword, $userId]);
            
            return ['success' => true, 'message' => '密码修改成功'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => '修改失败：' . $e->getMessage()];
        }
    }
}
?>
