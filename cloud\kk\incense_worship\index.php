<?php
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/incense.php';

$auth = new Auth();
$incenseService = new IncenseService();

// 检查用户是否登录
if (!isLoggedIn()) {
    redirectTo('login.php');
}

$userId = getCurrentUserId();

// 检查香的状态并给予奖励
$statusCheck = $incenseService->checkIncenseStatus($userId);

// 获取用户信息
$userInfo = $auth->getUserInfo($userId);

// 获取佛像列表
$buddhas = $incenseService->getBuddhas();

// 获取香品列表
$incenseTypes = $incenseService->getIncenseTypes();

// 获取用户当前燃烧的香
$burningIncense = $incenseService->getUserBurningIncense($userId);

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'burn_incense':
            $result = $incenseService->burnIncense(
                $userId, 
                $_POST['incense_type'], 
                $_POST['buddha_id'], 
                $_POST['wish_content'] ?? ''
            );
            echo json_encode($result);
            exit;
            
        case 'worship':
            $result = $incenseService->worship(
                $userId, 
                $_POST['buddha_id'], 
                $_POST['worship_type'] ?? 'bow',
                $_POST['prayer_content'] ?? ''
            );
            echo json_encode($result);
            exit;
            
        case 'make_wish':
            $result = $incenseService->makeWish(
                $userId, 
                $_POST['buddha_id'], 
                $_POST['wish_category'],
                $_POST['wish_content']
            );
            echo json_encode($result);
            exit;
            
        case 'check_status':
            $result = $incenseService->checkIncenseStatus($userId);
            echo json_encode($result);
            exit;
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - 网络礼佛道场</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="nav-container">
                <h1 class="site-title"><?php echo SITE_NAME; ?></h1>
                <div class="user-info">
                    <span class="username"><?php echo htmlspecialchars($userInfo['nickname']); ?></span>
                    <span class="gongde">功德: <span id="gongde-points"><?php echo $userInfo['gongde_points']; ?></span></span>
                    <a href="profile.php" class="btn btn-secondary">个人中心</a>
                    <a href="logout.php" class="btn btn-secondary">退出</a>
                </div>
            </div>
        </header>

        <!-- 状态提示 -->
        <?php if ($statusCheck['completed_count'] > 0): ?>
        <div class="alert alert-success">
            <?php echo $statusCheck['message']; ?>
        </div>
        <?php endif; ?>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="worship-area">
                <div class="buddha-section">
                    <h2>选择佛像</h2>
                    <div class="buddha-grid">
                        <?php foreach ($buddhas as $buddha): ?>
                        <div class="buddha-card" data-buddha-id="<?php echo $buddha['id']; ?>">
                            <div class="buddha-image">
                                <img src="images/buddhas/<?php echo $buddha['image']; ?>" 
                                     alt="<?php echo htmlspecialchars($buddha['name']); ?>"
                                     onerror="this.src='images/default-buddha.jpg'">
                            </div>
                            <div class="buddha-info">
                                <h3><?php echo htmlspecialchars($buddha['name']); ?></h3>
                                <p class="buddha-blessing"><?php echo htmlspecialchars($buddha['blessing_type']); ?></p>
                                <p class="buddha-bonus">功德加成: +<?php echo $buddha['gongde_bonus']; ?></p>
                                <p class="buddha-desc"><?php echo htmlspecialchars($buddha['description']); ?></p>
                                <div class="buddha-actions">
                                    <button class="btn btn-primary worship-btn" 
                                            data-buddha-id="<?php echo $buddha['id']; ?>">拜佛</button>
                                    <button class="btn btn-secondary wish-btn" 
                                            data-buddha-id="<?php echo $buddha['id']; ?>">许愿</button>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="incense-section">
                    <h2>选择香品</h2>
                    <div class="incense-grid">
                        <?php foreach ($incenseTypes as $incense): ?>
                        <div class="incense-card rarity-<?php echo $incense['rarity']; ?>" 
                             data-incense-id="<?php echo $incense['id']; ?>">
                            <div class="incense-image">
                                <img src="images/incense/<?php echo $incense['image']; ?>" 
                                     alt="<?php echo htmlspecialchars($incense['name']); ?>"
                                     onerror="this.src='images/default-incense.jpg'">
                            </div>
                            <div class="incense-info">
                                <h3><?php echo htmlspecialchars($incense['name']); ?></h3>
                                <p class="incense-cost">消耗: <?php echo $incense['gongde_cost']; ?> 功德</p>
                                <p class="incense-reward">奖励: <?php echo $incense['gongde_reward']; ?> 功德</p>
                                <p class="incense-duration">燃烧: <?php echo $incense['duration_minutes']; ?> 分钟</p>
                                <p class="incense-desc"><?php echo htmlspecialchars($incense['description']); ?></p>
                                <button class="btn btn-primary burn-incense-btn" 
                                        data-incense-id="<?php echo $incense['id']; ?>">烧香</button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- 当前燃烧的香 -->
            <?php if (!empty($burningIncense)): ?>
            <div class="burning-incense-section">
                <h2>正在燃烧的香</h2>
                <div class="burning-incense-list">
                    <?php foreach ($burningIncense as $burning): ?>
                    <div class="burning-incense-item">
                        <div class="incense-info">
                            <h4><?php echo htmlspecialchars($burning['incense_name']); ?></h4>
                            <p>敬献给: <?php echo htmlspecialchars($burning['buddha_name']); ?></p>
                            <?php if ($burning['wish_content']): ?>
                            <p class="wish-content">许愿: <?php echo htmlspecialchars($burning['wish_content']); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="burning-timer" data-end-time="<?php echo strtotime($burning['end_time']); ?>">
                            <div class="timer-display">--:--:--</div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- 统计信息 -->
            <div class="stats-section">
                <h2>功德统计</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>累计烧香</h3>
                        <div class="stat-number"><?php echo $userInfo['total_incense']; ?></div>
                        <div class="stat-label">次</div>
                    </div>
                    <div class="stat-card">
                        <h3>累计拜佛</h3>
                        <div class="stat-number"><?php echo $userInfo['total_worship']; ?></div>
                        <div class="stat-label">次</div>
                    </div>
                    <div class="stat-card">
                        <h3>累计许愿</h3>
                        <div class="stat-number"><?php echo $userInfo['total_wishes']; ?></div>
                        <div class="stat-label">次</div>
                    </div>
                    <div class="stat-card">
                        <h3>当前功德</h3>
                        <div class="stat-number"><?php echo $userInfo['gongde_points']; ?></div>
                        <div class="stat-label">点</div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 拜佛模态框 -->
    <div id="worship-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>拜佛</h2>
            <form id="worship-form">
                <input type="hidden" id="worship-buddha-id" name="buddha_id">
                <div class="form-group">
                    <label>拜佛方式:</label>
                    <select name="worship_type" required>
                        <option value="bow">鞠躬 (基础功德)</option>
                        <option value="kneel">跪拜 (1.5倍功德)</option>
                        <option value="prostrate">五体投地 (2倍功德)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>祈祷内容:</label>
                    <textarea name="prayer_content" placeholder="请输入您的祈祷内容..."></textarea>
                </div>
                <button type="submit" class="btn btn-primary">开始拜佛</button>
            </form>
        </div>
    </div>

    <!-- 许愿模态框 -->
    <div id="wish-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>许愿</h2>
            <form id="wish-form">
                <input type="hidden" id="wish-buddha-id" name="buddha_id">
                <div class="form-group">
                    <label>许愿类别:</label>
                    <select name="wish_category" required>
                        <option value="健康">健康平安</option>
                        <option value="事业">事业顺利</option>
                        <option value="学业">学业有成</option>
                        <option value="感情">感情美满</option>
                        <option value="财运">财运亨通</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>愿望内容:</label>
                    <textarea name="wish_content" placeholder="请输入您的愿望内容..." required></textarea>
                </div>
                <p class="cost-notice">许愿将消耗 20 功德点</p>
                <button type="submit" class="btn btn-primary">许愿</button>
            </form>
        </div>
    </div>

    <!-- 烧香模态框 -->
    <div id="incense-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>烧香</h2>
            <form id="incense-form">
                <input type="hidden" id="incense-type-id" name="incense_type">
                <div class="form-group">
                    <label>选择佛像:</label>
                    <select name="buddha_id" required>
                        <option value="">请选择佛像</option>
                        <?php foreach ($buddhas as $buddha): ?>
                        <option value="<?php echo $buddha['id']; ?>">
                            <?php echo htmlspecialchars($buddha['name']); ?> (<?php echo $buddha['blessing_type']; ?>)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label>许愿内容 (可选):</label>
                    <textarea name="wish_content" placeholder="您可以在烧香时同时许愿..."></textarea>
                </div>
                <button type="submit" class="btn btn-primary">开始烧香</button>
            </form>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
