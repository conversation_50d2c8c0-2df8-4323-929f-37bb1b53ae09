<?php
// 数据库配置文件
class Database {
    private $host = 'localhost';
    private $db_name = 'incense_worship';
    private $username = 'root';  // 请根据实际情况修改
    private $password = '';      // 请根据实际情况修改
    private $conn;
    
    public function connect() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                'mysql:host=' . $this->host . ';dbname=' . $this->db_name . ';charset=utf8mb4',
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                )
            );
        } catch(PDOException $e) {
            echo 'Connection Error: ' . $e->getMessage();
        }
        
        return $this->conn;
    }
}

// 全局数据库连接函数
function getDBConnection() {
    $database = new Database();
    return $database->connect();
}

// 系统配置
define('SITE_NAME', '网络烧香拜佛');
define('SITE_URL', 'http://localhost/incense_worship');
define('UPLOAD_PATH', 'uploads/');
define('DEFAULT_AVATAR', 'images/default_avatar.jpg');

// 会话配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // 在HTTPS环境下设置为1
session_start();

// 通用函数
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function getCurrentUserId() {
    return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
}

function redirectTo($url) {
    header("Location: $url");
    exit();
}

function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function formatDateTime($datetime) {
    return date('Y-m-d H:i:s', strtotime($datetime));
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return '刚刚';
    if ($time < 3600) return floor($time/60) . '分钟前';
    if ($time < 86400) return floor($time/3600) . '小时前';
    if ($time < 2592000) return floor($time/86400) . '天前';
    if ($time < 31536000) return floor($time/2592000) . '个月前';
    return floor($time/31536000) . '年前';
}
?>
