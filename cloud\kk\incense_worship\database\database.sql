-- 网络烧香拜佛系统数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS incense_worship CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE incense_worship;

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    avatar VARCHAR(255) DEFAULT 'default_avatar.jpg',
    gongde_points INT DEFAULT 100, -- 功德积分，新用户默认100
    total_incense INT DEFAULT 0, -- 总烧香次数
    total_worship INT DEFAULT 0, -- 总拜佛次数
    total_wishes INT DEFAULT 0, -- 总许愿次数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'banned') DEFAULT 'active'
);

-- 佛像表
CREATE TABLE buddhas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    blessing_type VARCHAR(50), -- 保佑类型：事业、健康、学业、财运等
    gongde_bonus INT DEFAULT 10, -- 拜此佛额外获得的功德加成
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 香品表
CREATE TABLE incense_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    gongde_cost INT DEFAULT 10, -- 烧香消耗的功德点数
    gongde_reward INT DEFAULT 15, -- 烧香获得的功德奖励
    duration_minutes INT DEFAULT 30, -- 香燃烧持续时间（分钟）
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 烧香记录表
CREATE TABLE incense_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    incense_type_id INT NOT NULL,
    buddha_id INT NOT NULL,
    gongde_cost INT,
    gongde_reward INT,
    wish_content TEXT, -- 许愿内容
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL, -- 香燃尽时间
    status ENUM('burning', 'completed', 'expired') DEFAULT 'burning',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (incense_type_id) REFERENCES incense_types(id),
    FOREIGN KEY (buddha_id) REFERENCES buddhas(id)
);

-- 拜佛记录表
CREATE TABLE worship_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    buddha_id INT NOT NULL,
    worship_type ENUM('bow', 'kneel', 'prostrate') DEFAULT 'bow', -- 拜佛方式
    gongde_reward INT DEFAULT 5,
    prayer_content TEXT, -- 祈祷内容
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (buddha_id) REFERENCES buddhas(id)
);

-- 许愿表
CREATE TABLE wishes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    buddha_id INT NOT NULL,
    wish_category VARCHAR(50), -- 许愿类别：事业、健康、学业、感情等
    wish_content TEXT NOT NULL,
    gongde_cost INT DEFAULT 20, -- 许愿消耗功德
    status ENUM('pending', 'fulfilled', 'expired') DEFAULT 'pending',
    fulfill_date DATE NULL, -- 用户标记愿望实现的日期
    expire_date DATE NULL, -- 愿望过期日期
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (buddha_id) REFERENCES buddhas(id)
);

-- 功德记录表
CREATE TABLE gongde_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action_type ENUM('incense', 'worship', 'wish', 'daily_bonus', 'admin_reward') NOT NULL,
    points_change INT NOT NULL, -- 正数为获得，负数为消耗
    description VARCHAR(255),
    related_id INT NULL, -- 关联的记录ID（如烧香记录ID）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 系统设置表
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入初始数据

-- 插入佛像数据
INSERT INTO buddhas (name, description, image, blessing_type, gongde_bonus) VALUES
('观世音菩萨', '大慈大悲，救苦救难，闻声救苦', 'guanyin.jpg', '健康平安', 15),
('文殊菩萨', '智慧第一，开启智慧，学业有成', 'wenshu.jpg', '学业智慧', 12),
('财神爷', '招财进宝，财源广进，事业兴旺', 'caishen.jpg', '财运事业', 18),
('药师佛', '消灾延寿，身体健康，疾病康复', 'yaoshi.jpg', '健康长寿', 20),
('地藏菩萨', '大愿地藏，超度众生，消除业障', 'dizang.jpg', '消灾解难', 10);

-- 插入香品数据
INSERT INTO incense_types (name, description, image, gongde_cost, gongde_reward, duration_minutes, rarity) VALUES
('普通檀香', '清香淡雅，最常用的香品', 'putong_tanxiang.jpg', 5, 8, 15, 'common'),
('高级沉香', '香气浓郁，功德加成更多', 'gaoji_chenxiang.jpg', 15, 25, 30, 'rare'),
('极品龙涎香', '珍贵稀有，功德倍增', 'jipin_longxian.jpg', 30, 50, 60, 'epic'),
('传说凤凰香', '传说中的神香，功德极大提升', 'chuanshuo_fenghuang.jpg', 50, 100, 120, 'legendary'),
('祈福香', '专门用于祈福的特制香品', 'qifu_xiang.jpg', 10, 15, 20, 'common'),
('平安香', '保平安的专用香', 'pingan_xiang.jpg', 8, 12, 18, 'common');

-- 插入系统设置
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('daily_gongde_bonus', '10', '每日登录功德奖励'),
('max_incense_per_day', '10', '每日最多烧香次数'),
('max_worship_per_day', '20', '每日最多拜佛次数'),
('max_wishes_per_day', '3', '每日最多许愿次数'),
('wish_expire_days', '365', '许愿过期天数');

-- 创建索引以提高查询性能
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_incense_records_user_id ON incense_records(user_id);
CREATE INDEX idx_incense_records_status ON incense_records(status);
CREATE INDEX idx_worship_records_user_id ON worship_records(user_id);
CREATE INDEX idx_wishes_user_id ON wishes(user_id);
CREATE INDEX idx_wishes_status ON wishes(status);
CREATE INDEX idx_gongde_records_user_id ON gongde_records(user_id);
